import { NextResponse } from 'next/server';
import { prisma } from '@/lib/prisma';
import { getUserFromRequest } from '@/lib/auth';
import { createAlipayOrder, createWechatOrder } from '@/lib/payment';

// 订阅计划价格配置
const PLAN_PRICES = {
  monthly: 20,
  yearly: 120
};

export async function POST(request) {
  try {
    // 验证用户身份
    const tokenPayload = getUserFromRequest(request);
    if (!tokenPayload) {
      return NextResponse.json(
        { error: '未授权访问' },
        { status: 401 }
      );
    }

    const { planType, paymentMethod } = await request.json();

    // 验证计划类型
    if (!planType || !PLAN_PRICES[planType]) {
      return NextResponse.json(
        { error: '无效的订阅计划' },
        { status: 400 }
      );
    }

    // 验证支付方式
    if (!paymentMethod || !['alipay', 'wechat'].includes(paymentMethod)) {
      return NextResponse.json(
        { error: '无效的支付方式' },
        { status: 400 }
      );
    }

    const amount = PLAN_PRICES[planType];

    // 创建支付记录
    const payment = await prisma.payment.create({
      data: {
        userId: tokenPayload.userId,
        amount,
        currency: 'CNY',
        paymentMethod,
        paymentStatus: 'pending'
      }
    });

    // 生成支付订单号
    const orderNo = `YP${Date.now()}${payment.id}`;

    // 创建支付订单
    let paymentUrl;
    const orderData = {
      orderNo,
      amount,
      subject: `悠悠有品${planType === 'monthly' ? '月度' : '年度'}会员`,
      body: `悠悠有品饰品监控${planType === 'monthly' ? '月度' : '年度'}会员订阅`
    };

    try {
      if (paymentMethod === 'alipay') {
        paymentUrl = await createAlipayOrder(orderData);
      } else if (paymentMethod === 'wechat') {
        paymentUrl = await createWechatOrder(orderData);
      } else {
        throw new Error('不支持的支付方式');
      }
    } catch (error) {
      console.error('创建支付订单失败:', error);
      // 删除已创建的支付记录
      await prisma.payment.delete({ where: { id: payment.id } });
      return NextResponse.json(
        { error: '创建支付订单失败: ' + error.message },
        { status: 500 }
      );
    }

    // 更新支付记录的交易ID
    await prisma.payment.update({
      where: { id: payment.id },
      data: { transactionId: orderNo }
    });

    return NextResponse.json({
      message: '支付订单创建成功',
      payment: {
        id: payment.id,
        orderNo,
        amount,
        currency: 'CNY',
        paymentMethod,
        paymentUrl,
        planType
      }
    });

  } catch (error) {
    console.error('创建支付订单错误:', error);
    return NextResponse.json(
      { error: '创建支付订单失败' },
      { status: 500 }
    );
  }
}

// 生成模拟支付链接（实际项目中需要调用真实支付API）
function generateMockPaymentUrl(orderNo, amount, paymentMethod) {
  const baseUrl = process.env.NEXT_PUBLIC_BASE_URL || 'http://localhost:3001';
  
  if (paymentMethod === 'alipay') {
    // 模拟支付宝支付链接
    return `${baseUrl}/payment/mock?orderNo=${orderNo}&amount=${amount}&method=alipay`;
  } else if (paymentMethod === 'wechat') {
    // 模拟微信支付链接
    return `${baseUrl}/payment/mock?orderNo=${orderNo}&amount=${amount}&method=wechat`;
  }
  
  return null;
}
