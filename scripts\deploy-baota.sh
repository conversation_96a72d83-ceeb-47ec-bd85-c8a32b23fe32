#!/bin/bash

# 宝塔面板环境部署脚本
# 适用于阿里云轻量服务器 + 宝塔面板
# 使用方法: ./scripts/deploy-baota.sh

set -e

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m'

# 项目路径 (宝塔默认网站目录)
PROJECT_PATH="/www/wwwroot/youpin-sentinel"

echo -e "${GREEN}🚀 悠悠有品 - 宝塔面板部署脚本${NC}"
echo "=================================================="

# 检查是否在正确的目录
check_directory() {
    if [ ! -f "package.json" ]; then
        echo -e "${RED}❌ 请在项目根目录执行此脚本${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 项目目录检查通过${NC}"
}

# 检查 Node.js 环境
check_nodejs() {
    echo -e "${BLUE}🔍 检查 Node.js 环境...${NC}"
    
    if ! command -v node &> /dev/null; then
        echo -e "${RED}❌ Node.js 未安装${NC}"
        echo -e "${YELLOW}请在宝塔面板安装 Node.js 版本管理器${NC}"
        exit 1
    fi
    
    NODE_VERSION=$(node -v)
    echo -e "${GREEN}✅ Node.js 版本: $NODE_VERSION${NC}"
    
    if ! command -v npm &> /dev/null; then
        echo -e "${RED}❌ npm 未安装${NC}"
        exit 1
    fi
    
    NPM_VERSION=$(npm -v)
    echo -e "${GREEN}✅ npm 版本: $NPM_VERSION${NC}"
}

# 检查环境变量配置
check_env_config() {
    echo -e "${BLUE}📋 检查环境配置...${NC}"
    
    if [ ! -f ".env.production" ]; then
        echo -e "${YELLOW}⚠️ 未找到 .env.production 文件${NC}"
        echo -e "${BLUE}正在创建配置文件...${NC}"
        
        if [ -f ".env.production.example" ]; then
            cp .env.production.example .env.production
            echo -e "${YELLOW}📝 请编辑 .env.production 文件，修改以下配置:${NC}"
            echo -e "  • JWT_SECRET (必须修改)"
            echo -e "  • NEXTAUTH_SECRET (必须修改)"
            echo -e "  • NEXT_PUBLIC_BASE_URL (改为你的域名)"
            echo -e "  • NEXTAUTH_URL (改为你的域名)"
            echo ""
            echo -e "${RED}⚠️ 请修改配置后重新运行此脚本${NC}"
            exit 1
        else
            echo -e "${RED}❌ 未找到配置模板文件${NC}"
            exit 1
        fi
    fi
    
    # 检查关键配置
    source .env.production
    
    if [ -z "$JWT_SECRET" ] || [ "$JWT_SECRET" = "your-super-secret-jwt-key-change-this-in-production" ]; then
        echo -e "${RED}❌ JWT_SECRET 未设置或使用默认值${NC}"
        echo -e "${YELLOW}请修改 .env.production 中的 JWT_SECRET${NC}"
        exit 1
    fi
    
    echo -e "${GREEN}✅ 环境配置检查通过${NC}"
}

# 安装依赖
install_dependencies() {
    echo -e "${BLUE}📦 安装项目依赖...${NC}"
    
    # 清理可能的缓存
    rm -rf node_modules package-lock.json
    
    # 安装依赖
    npm install
    
    echo -e "${GREEN}✅ 依赖安装完成${NC}"
}

# 初始化数据库
init_database() {
    echo -e "${BLUE}🗄️ 初始化数据库...${NC}"

    # 创建数据目录
    mkdir -p data

    # 确保 .env 文件存在并配置正确
    if [ ! -f ".env" ]; then
        echo -e "${YELLOW}📝 创建 .env 文件...${NC}"
        cp .env.production .env
    else
        echo -e "${BLUE}📝 检查 .env 文件配置...${NC}"
        # 确保数据库路径正确
        if grep -q "file:./prisma/dev.db" .env; then
            echo -e "${YELLOW}📝 更新数据库路径...${NC}"
            sed -i 's|DATABASE_URL="file:./prisma/dev.db"|DATABASE_URL="file:./data/production.db"|g' .env
        fi
        # 如果没有生产环境配置，则复制
        if ! grep -q "file:./data/production.db" .env; then
            echo -e "${YELLOW}📝 使用生产环境配置...${NC}"
            cp .env.production .env
        fi
    fi

    # 设置权限 (宝塔环境)
    chown -R www:www data 2>/dev/null || true
    chmod -R 755 data 2>/dev/null || true

    # 生成 Prisma 客户端
    npx prisma generate

    # 推送数据库模式
    npx prisma db push

    echo -e "${GREEN}✅ 数据库初始化完成${NC}"
}

# 构建应用
build_application() {
    echo -e "${BLUE}🔨 构建生产版本...${NC}"

    # 设置生产环境
    export NODE_ENV=production

    # 构建应用
    echo -e "${BLUE}📦 开始构建，这可能需要几分钟...${NC}"
    if npm run build; then
        echo -e "${GREEN}✅ 应用构建完成${NC}"
    else
        echo -e "${RED}❌ 应用构建失败${NC}"
        echo -e "${YELLOW}💡 常见解决方案:${NC}"
        echo -e "  1. 检查 Node.js 版本是否为 18+: node -v"
        echo -e "  2. 清理缓存: rm -rf .next node_modules package-lock.json && npm install"
        echo -e "  3. 检查代码语法错误"
        exit 1
    fi
}

# 创建超级管理员
create_admin() {
    echo -e "${BLUE}👤 创建超级管理员...${NC}"

    if node scripts/create-super-admin.js; then
        echo -e "${GREEN}✅ 超级管理员创建完成${NC}"
        echo -e "${BLUE}📋 默认登录信息:${NC}"
        echo -e "  邮箱: <EMAIL>"
        echo -e "  密码: Admin123456"
        echo -e "${RED}⚠️ 请首次登录后立即修改密码！${NC}"
    else
        echo -e "${YELLOW}⚠️ 超级管理员可能已存在，跳过创建${NC}"
    fi
}

# 配置 PM2
setup_pm2() {
    echo -e "${BLUE}⚙️ 配置 PM2 进程管理...${NC}"

    # 检查 PM2 是否安装
    if ! command -v pm2 &> /dev/null; then
        echo -e "${YELLOW}📦 安装 PM2...${NC}"
        npm install -g pm2
    fi

    # 检查端口占用
    if netstat -tlnp 2>/dev/null | grep -q ":3000 "; then
        echo -e "${YELLOW}⚠️ 端口 3000 被占用，尝试停止相关进程...${NC}"
        # 尝试停止可能的 PM2 进程
        pm2 stop all 2>/dev/null || true
        pm2 delete all 2>/dev/null || true
        # 等待端口释放
        sleep 2
    fi

    # 停止现有进程
    pm2 stop youpin-sentinel 2>/dev/null || true
    pm2 delete youpin-sentinel 2>/dev/null || true

    # 启动应用
    echo -e "${BLUE}🚀 启动应用...${NC}"
    if pm2 start npm --name "youpin-sentinel" -- start; then
        echo -e "${GREEN}✅ 应用启动成功${NC}"

        # 等待应用启动
        sleep 3

        # 检查应用状态
        if pm2 list | grep -q "youpin-sentinel.*online"; then
            echo -e "${GREEN}✅ 应用运行正常${NC}"
        else
            echo -e "${RED}❌ 应用启动失败，查看日志:${NC}"
            pm2 logs youpin-sentinel --lines 10
            exit 1
        fi
    else
        echo -e "${RED}❌ PM2 启动失败${NC}"
        exit 1
    fi

    # 保存配置
    pm2 save

    # 设置开机自启
    pm2 startup

    echo -e "${GREEN}✅ PM2 配置完成${NC}"
}

# 设置文件权限 (宝塔环境)
set_permissions() {
    echo -e "${BLUE}🔐 设置文件权限...${NC}"
    
    # 设置项目文件权限
    chown -R www:www .
    find . -type f -exec chmod 644 {} \;
    find . -type d -exec chmod 755 {} \;
    
    # 脚本文件需要执行权限
    chmod +x scripts/*.sh
    
    echo -e "${GREEN}✅ 权限设置完成${NC}"
}

# 创建备份计划任务提示
setup_backup_reminder() {
    echo -e "${BLUE}📅 备份计划提醒${NC}"
    echo -e "${YELLOW}建议在宝塔面板设置定时备份:${NC}"
    echo -e "  1. 进入宝塔面板 → 计划任务"
    echo -e "  2. 添加 Shell 脚本任务"
    echo -e "  3. 执行周期: 每天 02:00"
    echo -e "  4. 脚本内容: cd $PROJECT_PATH && ./scripts/backup-sqlite.sh"
}

# 显示部署结果
show_result() {
    echo ""
    echo -e "${GREEN}🎉 部署完成！${NC}"
    echo "=================================================="
    echo -e "${BLUE}📋 部署信息:${NC}"
    echo -e "  • 项目路径: $PROJECT_PATH"
    echo -e "  • 应用状态: ${GREEN}运行中${NC}"
    echo -e "  • 进程管理: PM2"
    echo -e "  • 运行端口: 3000"
    echo ""

    # 显示应用状态
    echo -e "${BLUE}📊 应用状态:${NC}"
    pm2 list | grep youpin-sentinel || echo -e "${YELLOW}  未找到 PM2 进程${NC}"
    echo ""

    echo -e "${BLUE}🔧 管理命令:${NC}"
    echo -e "  • 查看状态: ${YELLOW}pm2 status${NC}"
    echo -e "  • 查看日志: ${YELLOW}pm2 logs youpin-sentinel${NC}"
    echo -e "  • 重启应用: ${YELLOW}pm2 restart youpin-sentinel${NC}"
    echo -e "  • 停止应用: ${YELLOW}pm2 stop youpin-sentinel${NC}"
    echo ""

    echo -e "${BLUE}🌐 下一步操作:${NC}"
    echo -e "  1. 在宝塔面板添加网站并配置反向代理"
    echo -e "     - 域名: 你的域名"
    echo -e "     - 反向代理: http://127.0.0.1:3000"
    echo -e "  2. 申请 SSL 证书 (Let's Encrypt)"
    echo -e "  3. 测试访问: http://服务器IP:3000 (临时)"
    echo ""

    echo -e "${BLUE}🔑 默认管理员账户:${NC}"
    echo -e "  • 邮箱: ${YELLOW}<EMAIL>${NC}"
    echo -e "  • 密码: ${YELLOW}Admin123456${NC}"
    echo -e "  • ${RED}⚠️ 请首次登录后立即修改密码！${NC}"
    echo ""

    echo -e "${BLUE}🔍 健康检查:${NC}"
    echo -e "  • 本地测试: ${YELLOW}curl http://localhost:3000/api/health${NC}"
    echo -e "  • 外网测试: ${YELLOW}curl http://服务器IP:3000/api/health${NC}"
    echo ""

    setup_backup_reminder

    echo ""
    echo -e "${GREEN}✅ 部署成功！应用已在后台运行${NC}"
}

# 主函数
main() {
    check_directory
    check_nodejs
    check_env_config
    install_dependencies
    init_database
    build_application
    create_admin
    setup_pm2
    set_permissions
    show_result
}

# 执行主函数
main "$@"
